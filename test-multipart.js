const FormData = require('form-data');
const fs = require('fs');
const fetch = require('node-fetch');

async function testMultipartUpload() {
  try {
    // Buat file test sederhana
    const testImagePath = 'test-image.png';
    const testImageBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==', 'base64');
    fs.writeFileSync(testImagePath, testImageBuffer);
    
    // Buat FormData
    const form = new FormData();
    form.append('cover', fs.createReadStream(testImagePath), {
      filename: 'test.png',
      contentType: 'image/png'
    });
    
    // Test upload
    console.log('Testing multipart upload...');
    const response = await fetch('http://localhost:5000/albums/test-album-id/covers', {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    // Cleanup
    fs.unlinkSync(testImagePath);
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testMultipartUpload();
