require('dotenv').config();
const { Pool } = require('pg');

async function testDatabaseConnection() {
  const pool = new Pool();
  
  try {
    // Test koneksi database
    console.log('Testing database connection...');
    const result = await pool.query('SELECT NOW()');
    console.log('✓ Database connection successful');
    
    // Cek tabel albums
    console.log('Checking albums table...');
    const albumsResult = await pool.query('SELECT * FROM albums LIMIT 1');
    console.log('✓ Albums table exists');
    
    // Cek kolom cover_url
    console.log('Checking cover_url column...');
    const columnResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'albums' AND column_name = 'cover_url'
    `);
    
    if (columnResult.rows.length > 0) {
      console.log('✓ cover_url column exists');
    } else {
      console.log('✗ cover_url column does not exist');
      console.log('Adding cover_url column...');
      await pool.query('ALTER TABLE albums ADD COLUMN cover_url TEXT');
      console.log('✓ cover_url column added successfully');
    }
    
  } catch (error) {
    console.error('Database error:', error.message);
  } finally {
    await pool.end();
  }
}

testDatabaseConnection();
