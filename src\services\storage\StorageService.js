const fs = require('fs');
 
class StorageService {
  constructor(folder) {
    this._folder = folder;
 
    if (!fs.existsSync(folder)) {
      fs.mkdirSync(folder, { recursive: true });
    }
  }
 
  writeFile(file, meta) {
    const filename = +new Date() + meta.filename;
    const path = `${this._folder}/${filename}`;

    const fileStream = fs.createWriteStream(path);

    return new Promise((resolve, reject) => {
      fileStream.on('error', (error) => {
        console.error('Error writing file:', error);
        reject(error);
      });

      file.on('error', (error) => {
        console.error('Error reading file:', error);
        reject(error);
      });

      file.pipe(fileStream);
      file.on('end', () => resolve(filename));
    });
  }
}
 
module.exports = StorageService;