const { Pool } = require('pg');
const { nanoid } = require('nanoid');
const InvariantError = require('../../exceptions/InvariantError');
const NotFoundError = require('../../exceptions/NotFoundError');

class SongsService {
    constructor() {
        this._pool = new Pool();
    }

    async addSong({ title, performer, year, genre, duration, albumId }) {
        const id = nanoid(16);

        const query = {
            text: `INSERT INTO songs (
    id, title, performer, year, genre, duration, "albumId"
  ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`,
            values: [id, title, performer, year, genre, duration, albumId],
        };

        const result = await this._pool.query(query);

        if (!result.rows[0].id) {
            throw new InvariantError('Lagu gagal ditambahkan');
        }

        return result.rows[0].id;
    }

    async getSongs() {
        const result = await this._pool.query('SELECT id, title, performer FROM songs');
        return result.rows;
    }

    async getSongsByAlbumId(albumId) {
        const query = {
            text: 'SELECT id, title, performer FROM songs WHERE "albumId" = $1',
            values: [albumId],
        };

        const result = await this._pool.query(query);
        return result.rows;
    }

    async getSongById(id) {
        const query = {
            text: 'SELECT * FROM songs WHERE id = $1',
            values: [id],
        };

        const result = await this._pool.query(query);

        if (!result.rows.length) {
            throw new NotFoundError('Lagu tidak ditemukan');
        }

        return result.rows[0];
    }

    async editSongById(id, { title, performer, year, genre, duration, albumId }) {
        const query = {
            text: `UPDATE songs SET title = $1, performer = $2,  year = $3, genre = $4, duration = $5, "albumId" = $6 WHERE id = $7 RETURNING id`,
            values: [title, performer, year, genre, duration, albumId, id],
        };

        const result = await this._pool.query(query);

        if (!result.rows.length) {
            throw new NotFoundError('Gagal memperbarui lagu. Id tidak ditemukan');
        }
    }

    async deleteSongById(id) {
        const query = {
            text: 'DELETE FROM songs WHERE id = $1 RETURNING id',
            values: [id],
        };

        const result = await this._pool.query(query);

        if (!result.rows.length) {
            throw new NotFoundError('Lagu gagal dihapus. Id tidak ditemukan');
        }
    }
}    

module.exports = SongsService;