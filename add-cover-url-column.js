require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool();

async function addCoverUrlColumn() {
  try {
    // Cek apakah kolom cover_url sudah ada
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'albums' AND column_name = 'cover_url'
    `;
    
    const checkResult = await pool.query(checkColumnQuery);
    
    if (checkResult.rows.length > 0) {
      console.log('Kolom cover_url sudah ada di tabel albums');
      return;
    }
    
    // Tambahkan kolom cover_url
    const addColumnQuery = 'ALTER TABLE albums ADD COLUMN cover_url TEXT';
    await pool.query(addColumnQuery);
    
    console.log('Kolom cover_url berhasil ditambahkan ke tabel albums');
  } catch (error) {
    console.error('Error menambahkan kolom cover_url:', error);
  } finally {
    await pool.end();
  }
}

addCoverUrlColumn();
