const ClientError = require('../../exceptions/ClientError');

class UploadsHandler {
  constructor(service, validator, albumsService) {
    this._service = service;
    this._validator = validator;
    this._albumsService = albumsService;

    this.postUploadCoverHandler = this.postUploadCoverHandler.bind(this);
  }

  async postUploadCoverHandler(request, h) {
    try {
      const { id: albumId } = request.params;

      // Debug logging
      console.log('Request payload:', request.payload);
      console.log('Request headers:', request.headers);

      const { cover } = request.payload;

      // Cek apakah cover ada dalam payload
      if (!cover) {
        return h.response({
          status: 'fail',
          message: 'Cover tidak ditemukan dalam payload',
        }).code(400);
      }

      // Debug logging untuk cover
      console.log('Cover object:', cover);
      console.log('Cover hapi headers:', cover.hapi?.headers);

      // Verifikasi album exists
      await this._albumsService.getAlbumById(albumId);

      // Cek apakah cover.hapi dan headers ada
      if (!cover.hapi || !cover.hapi.headers) {
        return h.response({
          status: 'fail',
          message: 'Header file tidak valid',
        }).code(400);
      }

      // Validasi MIME type
      this._validator.validateImageHeaders(cover.hapi.headers);

      const filename = await this._service.writeFile(cover, cover.hapi);
      const coverUrl = `http://${process.env.HOST}:${process.env.PORT}/upload/images/${filename}`;

      // Update album dengan cover URL
      await this._albumsService.updateAlbumCover(albumId, coverUrl);

      const response = h.response({
        status: 'success',
        message: 'Sampul berhasil diunggah',
      });
      response.code(201);
      return response;
    } catch (error) {
      if (error instanceof ClientError) {
        return h.response({
          status: 'fail',
          message: error.message,
        }).code(error.statusCode);
      }

      console.error(error);
      return h.response({
        status: 'error',
        message: 'Maaf, terjadi kesalahan pada server.',
      }).code(500);
    }
  }
}

module.exports = UploadsHandler;
